# 股票数据自动下载系统 (Stock Data Auto Download)

## 项目简介

这是一个基于Python开发的股票数据自动下载和管理系统，主要用于从Tushare获取股票相关数据并存储到MySQL数据库中。系统支持多种股票基础数据的获取，包括股票基本信息、公司管理层信息、沪深股通成分股、交易日历等。

## 主要功能

### 📊 数据获取模块
- **股票基础信息 (stock_basic)**: 获取所有股票的基本信息，包括代码、名称、所属行业、上市状态等
- **公司管理层信息 (stock_managers)**: 获取上市公司管理层人员信息
- **沪深股通成分股 (hs_const)**: 获取沪深港通标的成分和权重信息
- **股票更名信息 (namechange)**: 获取股票更名历史记录
- **公司信息 (stock_company)**: 获取上市公司基本信息
- **交易日历 (trade_cal)**: 获取交易所交易日历数据

### 🗄️ 数据存储
- 使用MySQL数据库存储所有获取的数据
- 支持自动创建数据表结构
- 数据增量更新和全量更新支持

### 📝 日志管理
- **更新日志**: 记录每日数据更新状态 (`update_logs/`)
- **错误日志**: 详细记录执行过程中的错误信息 (`err_logs/`)

## 技术栈

- **Python 3.x**: 主要开发语言
- **Tushare**: 金融数据接口
- **MySQL**: 数据存储
- **Pandas**: 数据处理
- **SQLAlchemy**: 数据库ORM
- **PyMySQL**: MySQL连接器

## 安装配置

### 1. 环境要求
```bash
Python >= 3.7
MySQL >= 5.7
```

### 2. 依赖安装
```bash
pip install pandas
pip install tushare
pip install sqlalchemy
pip install pymysql
pip install python-dotenv
```

### 3. 环境配置
在项目根目录创建 `.env` 文件：
```env
# MySQL数据库配置
MYSQL_HOST=localhost
MYSQL_USER=your_username
MYSQL_PWD=your_password
MYSQL_PORT=3306
MYSQL_DB=stock_data

# Tushare API Token
TS_TOKEN=your_tushare_token
```

### 4. 数据库初始化
确保MySQL服务运行，并创建对应的数据库：
```sql
CREATE DATABASE stock_data CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## 使用方法

### 单模块执行
```bash
# 更新股票基础信息
python stock/basic_data/stock_basic.py

# 更新管理层信息
python stock/basic_data/stock_managers.py

# 更新交易日历
python stock/basic_data/trade_cal.py
```

### 工具函数使用
```python
from utils import mysql_save_df, mysql_read_data

# 保存DataFrame到数据库
mysql_save_df(df, 'table_name')

# 从数据库读取数据
df = mysql_read_data("SELECT * FROM stock_basic")
```

## 项目结构

```
data_auto_download/
├── stock/                          # 股票数据模块
│   └── basic_data/                 # 基础数据获取
│       ├── stock_basic.py          # 股票基础信息
│       ├── stock_managers.py       # 管理层信息
│       ├── hs_const.py            # 沪深股通成分股
│       ├── namechange.py          # 股票更名信息
│       ├── stock_company.py       # 公司信息
│       ├── trade_cal.py           # 交易日历
│       └── SQL/                   # 数据表结构文件
├── update_logs/                   # 更新成功日志
├── err_logs/                      # 错误日志
├── utils.py                       # 工具函数
├── setting.py                     # 配置文件
└── basic_finance_sheet.py         # 财务数据模块(开发中)
```

## 日志系统

### 更新日志格式
- 路径: `update_logs/{模块名}/{功能名}.txt`
- 格式: `YYYY-MM-DD|SUCCESS` 或 `YYYY-MM-DD|ERROR`

### 错误日志格式
- 路径: `err_logs/{模块名}_{功能名}_{日期}.log`
- 包含完整的错误堆栈信息

## 注意事项

1. **API限制**: Tushare有接口调用频率限制，请合理安排数据获取任务
2. **数据时效**: 股票数据通常在交易日15:00后更新
3. **数据库连接**: 确保数据库连接稳定，建议使用连接池
4. **磁盘空间**: 股票历史数据量较大，请确保足够的存储空间

## 开发计划

- [ ] 添加更多股票数据类型支持
- [ ] 实现数据自动化调度
- [ ] 添加数据质量检查
- [ ] 开发Web管理界面
- [ ] 支持更多数据源

## 许可证

此项目仅供学习和研究使用，请遵守相关数据使用协议。

## 联系方式

如有问题或建议，请通过Issue提交。 