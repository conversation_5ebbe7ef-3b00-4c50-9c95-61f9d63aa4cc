-- 创建沪深股通成份股表 (hs_const)
-- 对应 Tushare Pro 的 hs_const 接口
-- 接口文档: https://tushare.pro/document/2?doc_id=104

CREATE TABLE IF NOT EXISTS `stock_hs_const` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
    `code` VARCHAR(20) NOT NULL COMMENT 'TS代码，股票代码.交易所，如 000001.SZ',
    `hs_type` VARCHAR(10) NOT NULL COMMENT '沪深港通类型：SH沪股通、SZ深股通',
    `in_date` DATE DEFAULT NULL COMMENT '纳入日期',
    `out_date` DATE DEFAULT NULL COMMENT '剔除日期',
    `is_new` VARCHAR(10) DEFAULT NULL COMMENT '是否最新：1是、0否',
    `last_update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    
    -- 主键
    PRIMARY KEY (`id`),
    
    -- 唯一索引：确保同一股票在同一类型下的唯一性
    UNIQUE KEY `uk_code_hs_type_in_date` (`code`, `hs_type`, `in_date`) COMMENT '股票代码+类型+纳入日期唯一索引',
    
    -- 普通索引：优化常用查询字段
    KEY `idx_code` (`code`) COMMENT '股票代码索引，用于按股票代码查询',
    KEY `idx_hs_type` (`hs_type`) COMMENT '沪深港通类型索引，用于按类型查询',
    KEY `idx_in_date` (`in_date`) COMMENT '纳入日期索引，用于按纳入日期查询',
    KEY `idx_out_date` (`out_date`) COMMENT '剔除日期索引，用于按剔除日期查询',
    KEY `idx_is_new` (`is_new`) COMMENT '是否最新索引，用于筛选最新成份股',
    
    -- 复合索引：优化常用查询场景
    KEY `idx_hs_type_is_new` (`hs_type`, `is_new`) COMMENT '复合索引：按类型+是否最新查询',
    KEY `idx_code_is_new` (`code`, `is_new`) COMMENT '复合索引：按股票代码+是否最新查询',
    KEY `idx_hs_type_in_date` (`hs_type`, `in_date`) COMMENT '复合索引：按类型+纳入日期查询'
    
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci 
  COMMENT='沪深股通成份股表 - 存储沪股通和深股通成份股历史变化数据'
  ROW_FORMAT=DYNAMIC;

-- 创建视图：仅显示当前最新的沪深股通成份股
CREATE OR REPLACE VIEW `v_hs_const_current` AS
SELECT 
    `code`,
    `hs_type`,
    `in_date`,
    `out_date`
FROM `hs_const`
WHERE `is_new` = '1'
ORDER BY `hs_type`, `code`;

-- 创建视图：沪股通当前成份股
CREATE OR REPLACE VIEW `v_hs_const_sh_current` AS
SELECT 
    `code`,
    `in_date`,
    `out_date`
FROM `hs_const`
WHERE `hs_type` = 'SH' AND `is_new` = '1'
ORDER BY `code`;

-- 创建视图：深股通当前成份股
CREATE OR REPLACE VIEW `v_hs_const_sz_current` AS
SELECT 
    `code`,
    `in_date`,
    `out_date`
FROM `hs_const`
WHERE `hs_type` = 'SZ' AND `is_new` = '1'
ORDER BY `code`;

-- 添加表注释和字段注释的详细说明
ALTER TABLE `hs_const` COMMENT = 
'沪深股通成份股表 - 存储沪股通和深股通成份股的历史变化数据
更新频率：每日更新
覆盖范围：包含沪股通和深股通所有历史成份股变化记录
主要用途：
1. 查询当前沪深股通成份股列表
2. 追踪成份股纳入和剔除的历史变化
3. 分析沪深股通成份股的变化趋势
4. 支持沪深港通相关的投资分析
5. 为沪深港通资金流向分析提供基础数据'; 