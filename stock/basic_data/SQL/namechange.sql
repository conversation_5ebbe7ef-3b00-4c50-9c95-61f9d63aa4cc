CREATE TABLE IF NOT EXISTS `stock_namechange` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
    `code` VARCHAR(20) NOT NULL COMMENT 'TS代码，如 600848.SH',
    `name` VARCHAR(100) NOT NULL COMMENT '证券名称（变更后的名称）',
    `start_date` DATE NOT NULL COMMENT '开始日期（名称生效开始）',
    `end_date` DATE DEFAULT NULL COMMENT '结束日期（名称生效结束，可能为空）',
    `ann_date` DATE DEFAULT NULL COMMENT '公告日期',
    `change_reason` VARCHAR(255) DEFAULT NULL COMMENT '变更原因',
    `last_update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',

    PRIMARY KEY (`id`),

    -- 避免重复：同一证券同一生效开始日期只保留一条
    UNIQUE KEY `uk_code_start_date` (`code`, `start_date`, `name`) COMMENT '唯一：代码+开始日期',

    -- 常用查询索引
    KEY `idx_code` (`code`) COMMENT '按股票代码查询',
    KEY `idx_start_date` (`start_date`) COMMENT '按开始日期查询',
    KEY `idx_ann_date` (`ann_date`) COMMENT '按公告日期查询'
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci 
  COMMENT='股票曾用名-名称变更记录' 
  ROW_FORMAT=DYNAMIC; 

  -- 添加表注释和字段注释的详细说明
  
