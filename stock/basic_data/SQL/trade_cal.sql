-- 交易日历表
-- 存储各大交易所的交易日历数据，包括交易日、休市日等信息
-- 数据来源：Tushare Pro trade_cal 接口
CREATE TABLE IF NOT EXISTS `trade_cal` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
    `exchange` VARCHAR(10) NOT NULL DEFAULT 'SSE' COMMENT '交易所代码：SSE上交所,SZSE深交所,CFFEX中金所,SHFE上期所,CZCE郑商所,DCE大商所,INE上能源',
    `cal_date` DATE NOT NULL COMMENT '日历日期，格式：YYYY-MM-DD',
    `is_open` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否交易日：0=休市，1=交易',
    `pre_date` DATE NULL COMMENT '上一个交易日',
    `next_date` DATE NULL COMMENT '下一个交易日',
    `last_update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    
    -- 主键
    PRIMARY KEY (`id`),
    
    -- 唯一索引：确保同一交易所的同一日期只有一条记录
    UNIQUE KEY `uk_exchange_cal_date` (`exchange`, `cal_date`),
    
    -- 普通索引：优化日期查询
    KEY `idx_cal_date` (`cal_date`) COMMENT '日历日期索引，用于按日期查询',
    KEY `idx_is_open` (`is_open`) COMMENT '交易状态索引，用于快速筛选交易日或休市日',
    KEY `idx_exchange` (`exchange`) COMMENT '交易所索引，用于按交易所查询',
    
    -- 复合索引：优化常用查询场景
    KEY `idx_exchange_is_open_cal_date` (`exchange`, `is_open`, `cal_date`) COMMENT '复合索引：按交易所+交易状态+日期查询',
    KEY `idx_is_open_cal_date` (`is_open`, `cal_date`) COMMENT '复合索引：按交易状态+日期查询，用于获取指定日期范围内的交易日',
    
    -- 外键索引：用于关联查询
    KEY `idx_pre_date` (`pre_date`) COMMENT '上一交易日索引',
    KEY `idx_next_date` (`next_date`) COMMENT '下一交易日索引'
    
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci 
  COMMENT='交易日历表 - 存储各大交易所的交易日历数据，包括交易日、休市日等信息'
  ROW_FORMAT=DYNAMIC;
-- 创建视图：仅显示交易日
CREATE OR REPLACE VIEW `trade_days` AS
SELECT 
    `exchange`,
    `cal_date`,
    `pre_date`,
    `next_date`
FROM `trade_cal` 
WHERE `is_open` = 1
ORDER BY `exchange`, `cal_date`;

-- 添加表注释和字段注释的详细说明
ALTER TABLE `trade_cal` COMMENT = 
'交易日历表 - 记录各大交易所的交易日历信息
更新频率：每日更新
覆盖范围：从2000年至明年12月31日
主要用途：
1. 判断某日期是否为交易日
2. 获取指定日期的上一个/下一个交易日
3. 计算交易日区间和交易天数
4. 支持股票、期货、期权等金融产品的交易日历查询';
