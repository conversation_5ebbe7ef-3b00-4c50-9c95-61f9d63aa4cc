-- 创建股票基本信息表 (stock_basic)
-- 对应 Tushare Pro 的 stock_basic 接口
-- 接口文档: https://tushare.pro/document/2?doc_id=25

CREATE TABLE IF NOT EXISTS `stock_basic` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
    `code` VARCHAR(20) NOT NULL COMMENT '股票代码，股票代码.交易所，如 000001.SZ',
    `symbol` VARCHAR(10) NOT NULL COMMENT '股票代码，如 000001',
    `name` VARCHAR(50) NOT NULL COMMENT '股票名称，如 贵州茅台',
    `area` VARCHAR(20) DEFAULT NULL COMMENT '地域，如 浙江',
    `industry` VARCHAR(50) DEFAULT NULL COMMENT '所属行业',
    `fullname` VARCHAR(100) DEFAULT NULL COMMENT '股票全称',
    `enname` VARCHAR(200) DEFAULT NULL COMMENT '英文全称',
    `cnspell` VARCHAR(100) DEFAULT NULL COMMENT '拼音缩写',
    `market` VARCHAR(20) DEFAULT NULL COMMENT '市场类型：主板、创业板、科创板、北交所、CDR',
    `exchange` VARCHAR(10) DEFAULT NULL COMMENT '交易所代码：SSE上交所、SZSE深交所、BSE北交所',
    `curr_type` VARCHAR(10) DEFAULT NULL COMMENT '交易货币',
    `list_status` VARCHAR(10) DEFAULT NULL COMMENT '上市状态：L上市、D退市、P暂停上市',
    `list_date` DATE DEFAULT NULL COMMENT '上市日期',
    `delist_date` DATE DEFAULT NULL COMMENT '退市日期',
    `is_hs` VARCHAR(10) DEFAULT NULL COMMENT '是否沪深港通标的：N否、H沪股通、S深股通',
    `act_name` VARCHAR(100) DEFAULT NULL COMMENT '实控人名称',
    `act_ent_type` VARCHAR(50) DEFAULT NULL COMMENT '实控人企业性质',
    `last_update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    
    -- 主键
    PRIMARY KEY (`id`),
    
    -- 唯一索引：确保股票代码唯一
    UNIQUE KEY `uk_code` (`code`) COMMENT '股票代码唯一索引',
    
    -- 普通索引：优化常用查询字段
    KEY `idx_symbol` (`symbol`) COMMENT '股票代码索引，用于按symbol查询',
    KEY `idx_name` (`name`) COMMENT '股票名称索引，用于按名称查询',
    KEY `idx_area` (`area`) COMMENT '地域索引，用于按地域查询',
    KEY `idx_industry` (`industry`) COMMENT '行业索引，用于按行业查询',
    KEY `idx_market` (`market`) COMMENT '市场类型索引，用于按市场查询',
    KEY `idx_exchange` (`exchange`) COMMENT '交易所索引，用于按交易所查询',
    KEY `idx_list_status` (`list_status`) COMMENT '上市状态索引，用于筛选上市状态',
    KEY `idx_list_date` (`list_date`) COMMENT '上市日期索引，用于按上市日期查询',
    KEY `idx_is_hs` (`is_hs`) COMMENT '沪深港通标的索引，用于筛选港股通标的',
    
    -- 复合索引：优化常用查询场景
    KEY `idx_exchange_list_status` (`exchange`, `list_status`) COMMENT '复合索引：按交易所+上市状态查询',
    KEY `idx_market_list_status` (`market`, `list_status`) COMMENT '复合索引：按市场类型+上市状态查询',
    KEY `idx_list_status_list_date` (`list_status`, `list_date`) COMMENT '复合索引：按上市状态+上市日期查询'
    
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci 
  COMMENT='股票基本信息表 - 存储A股市场所有股票的基本信息'
  ROW_FORMAT=DYNAMIC;

-- 创建视图：仅显示上市股票
CREATE OR REPLACE VIEW `v_stock_basic_listed` AS
SELECT 
    `code`,
    `symbol`,
    `name`,
    `area`,
    `industry`,
    `fullname`,
    `market`,
    `exchange`,
    `list_date`,
    `is_hs`,
    `act_name`,
    `act_ent_type`
FROM `stock_basic`
WHERE `list_status` = 'L'
ORDER BY `code`;

-- 添加表注释和字段注释的详细说明
ALTER TABLE `stock_basic` COMMENT = 
'股票基本信息表 - 存储A股市场所有股票的基本信息
更新频率：每日更新
覆盖范围：包含A股所有股票（上市、退市、暂停上市）
主要用途：
1. 股票基础信息查询
2. 按行业、地域、市场类型分类统计
3. 上市状态筛选和统计
4. 沪深港通标的筛选
5. 支持各类金融数据分析的基础数据源';