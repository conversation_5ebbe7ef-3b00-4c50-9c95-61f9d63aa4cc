-- 上市公司基本信息表
CREATE TABLE IF NOT EXISTS `stock_company` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
    `code` VARCHAR(20) NOT NULL COMMENT '股票代码，如 000001.SZ',
    `com_name` VARCHAR(255) DEFAULT NULL COMMENT '公司全称',
    `com_id` VARCHAR(50) DEFAULT NULL COMMENT '统一社会信用代码',
    `exchange` VARCHAR(10) NOT NULL COMMENT '交易所代码：SSE上交所、SZSE深交所、BSE北交所',
    `chairman` VARCHAR(100) DEFAULT NULL COMMENT '法人代表',
    `manager` VARCHAR(100) DEFAULT NULL COMMENT '总经理',
    `secretary` VARCHAR(100) DEFAULT NULL COMMENT '董秘',
    `reg_capital` DECIMAL(20,4) DEFAULT NULL COMMENT '注册资本(万元)',
    `setup_date` DATE DEFAULT NULL COMMENT '注册日期',
    `province` VARCHAR(50) DEFAULT NULL COMMENT '所在省份',
    `city` VARCHAR(100) DEFAULT NULL COMMENT '所在城市',
    `introduction` TEXT DEFAULT NULL COMMENT '公司介绍',
    `website` VARCHAR(255) DEFAULT NULL COMMENT '公司主页',
    `email` VARCHAR(255) DEFAULT NULL COMMENT '电子邮件',
    `office` TEXT DEFAULT NULL COMMENT '办公室地址',
    `employees` INT DEFAULT NULL COMMENT '员工人数',
    `main_business` TEXT DEFAULT NULL COMMENT '主要业务及产品',
    `business_scope` TEXT DEFAULT NULL COMMENT '经营范围',
    `last_update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    
    PRIMARY KEY (`id`) COMMENT '主键ID',
    
    -- 唯一索引：确保股票代码的唯一性
    UNIQUE KEY `uk_code` (`code`) COMMENT '股票代码唯一索引',
    
    -- 普通索引：优化常用查询字段
    KEY `idx_exchange` (`exchange`) COMMENT '交易所索引，用于按交易所查询',
    KEY `idx_com_name` (`com_name`) COMMENT '公司名称索引，用于按公司名称查询',
    KEY `idx_province` (`province`) COMMENT '省份索引，用于按省份查询',
    KEY `idx_city` (`city`) COMMENT '城市索引，用于按城市查询',
    KEY `idx_setup_date` (`setup_date`) COMMENT '注册日期索引，用于按注册日期查询',
    KEY `idx_chairman` (`chairman`) COMMENT '法人代表索引，用于按法人代表查询',
    KEY `idx_reg_capital` (`reg_capital`) COMMENT '注册资本索引，用于按注册资本查询',
    
    -- 复合索引：优化常用查询场景
    KEY `idx_exchange_province` (`exchange`, `province`) COMMENT '复合索引：按交易所+省份查询',
    KEY `idx_province_city` (`province`, `city`) COMMENT '复合索引：按省份+城市查询',
    KEY `idx_exchange_setup_date` (`exchange`, `setup_date`) COMMENT '复合索引：按交易所+注册日期查询'
    
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci 
  COMMENT='上市公司基本信息表，存储A股所有上市公司的基础信息'
  ROW_FORMAT=DYNAMIC;

-- 创建视图：按交易所分类的公司信息
CREATE OR REPLACE VIEW `v_stock_company_sse` AS
SELECT 
    `code`,
    `com_name`,
    `chairman`,
    `manager`,
    `secretary`,
    `reg_capital`,
    `setup_date`,
    `province`,
    `city`,
    `website`,
    `employees`
FROM `stock_company`
WHERE `exchange` = 'SSE'
ORDER BY `code`;

-- 创建视图：深交所公司信息
CREATE OR REPLACE VIEW `v_stock_company_szse` AS
SELECT 
    `code`,
    `com_name`,
    `chairman`,
    `manager`,
    `secretary`,
    `reg_capital`,
    `setup_date`,
    `province`,
    `city`,
    `website`,
    `employees`
FROM `stock_company`
WHERE `exchange` = 'SZSE'
ORDER BY `code`;

-- 创建视图：北交所公司信息
CREATE OR REPLACE VIEW `v_stock_company_bse` AS
SELECT 
    `code`,
    `com_name`,
    `chairman`,
    `manager`,
    `secretary`,
    `reg_capital`,
    `setup_date`,
    `province`,
    `city`,
    `website`,
    `employees`
FROM `stock_company`
WHERE `exchange` = 'BSE'
ORDER BY `code`;

-- 创建视图：按省份统计公司数量
CREATE OR REPLACE VIEW `v_stock_company_province_stat` AS
SELECT 
    `province`,
    `exchange`,
    COUNT(*) as `company_count`,
    SUM(`reg_capital`) as `total_reg_capital`,
    AVG(`reg_capital`) as `avg_reg_capital`,
    SUM(`employees`) as `total_employees`,
    AVG(`employees`) as `avg_employees`
FROM `stock_company`
WHERE `province` IS NOT NULL AND `province` != ''
GROUP BY `province`, `exchange`
ORDER BY `province`, `exchange`;

-- 添加表注释
ALTER TABLE `stock_company` COMMENT = '上市公司基本信息表，存储A股所有上市公司的基础信息，数据来源：TuShare pro.stock_company接口'; 