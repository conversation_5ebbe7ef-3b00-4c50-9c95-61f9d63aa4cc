-- 上市公司管理层信息表
CREATE TABLE IF NOT EXISTS `stock_managers` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
    `code` VARCHAR(20) NOT NULL COMMENT '股票代码，如 000001.SZ',
    `ann_date` VARCHAR(8) NOT NULL COMMENT '公告日期，格式YYYYMMDD',
    `name` VARCHAR(100) DEFAULT NULL COMMENT '管理层姓名',
    `gender` VARCHAR(10) DEFAULT NULL COMMENT '性别，M男性 F女性',
    `lev` VARCHAR(50) DEFAULT NULL COMMENT '岗位类别',
    `title` VARCHAR(100) DEFAULT NULL COMMENT '具体岗位',
    `edu` VARCHAR(50) DEFAULT NULL COMMENT '学历',
    `national` VARCHAR(50) DEFAULT NULL COMMENT '国籍',
    `birthday` VARCHAR(20) DEFAULT NULL COMMENT '出生年月',
    `begin_date` VARCHAR(8) DEFAULT NULL COMMENT '上任日期，格式YYYYMMDD',
    `end_date` VARCHAR(8) DEFAULT NULL COMMENT '离任日期，格式YYYYMMDD',
    `resume` TEXT DEFAULT NULL COMMENT '个人简历',
    `last_update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    
    PRIMARY KEY (`id`) COMMENT '主键ID',
    
    -- 复合索引：股票代码、公告日期、姓名的组合唯一性
    UNIQUE KEY `uk_code_ann_name` (`code`, `ann_date`, `name`, `title`) COMMENT '股票代码、公告日期、姓名、职位组合唯一索引',
    
    -- 普通索引：优化常用查询字段
    KEY `idx_code` (`code`) COMMENT '股票代码索引，用于按股票查询',
    KEY `idx_ann_date` (`ann_date`) COMMENT '公告日期索引，用于按公告日期查询',
    KEY `idx_name` (`name`) COMMENT '姓名索引，用于按姓名查询',
    KEY `idx_title` (`title`) COMMENT '职位索引，用于按职位查询',
    KEY `idx_begin_date` (`begin_date`) COMMENT '上任日期索引，用于按上任日期查询',
    KEY `idx_end_date` (`end_date`) COMMENT '离任日期索引，用于按离任日期查询'
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='上市公司管理层信息表';

-- 为表添加分区（按年份分区，提高查询性能）
-- ALTER TABLE stock_managers 
-- PARTITION BY RANGE (YEAR(STR_TO_DATE(ann_date, '%Y%m%d'))) (
--     PARTITION p2020 VALUES LESS THAN (2021),
--     PARTITION p2021 VALUES LESS THAN (2022),
--     PARTITION p2022 VALUES LESS THAN (2023),
--     PARTITION p2023 VALUES LESS THAN (2024),
--     PARTITION p2024 VALUES LESS THAN (2025),
--     PARTITION p2025 VALUES LESS THAN (2026),
--     PARTITION p_max VALUES LESS THAN MAXVALUE
-- );

-- 数据维护说明：
-- 1. 该表存储上市公司管理层的详细信息
-- 2. 通过stock_code和ann_date建立关联关系
-- 3. 支持按时间、公司、人员等多维度查询
-- 4. resume字段存储个人简历，可能较长，使用TEXT类型
-- 5. 生日字段可能包含年份或者具体日期，使用VARCHAR存储原始格式
-- 6. 日期字段保持YYYYMMDD格式与源数据一致，便于增量更新判断

-- 常用查询示例：
-- 查询某公司最新管理层信息：
-- SELECT * FROM stock_managers WHERE code = '000001.SZ' ORDER BY ann_date DESC;

-- 查询某时间段内的管理层变动：
-- SELECT * FROM stock_managers WHERE ann_date BETWEEN '20240101' AND '20241231';

-- 查询现任管理层（未离任）：
-- SELECT * FROM stock_managers WHERE end_date IS NULL OR end_date = '';

-- 查询某个人的任职历史：
-- SELECT * FROM stock_managers WHERE name = '姓名' ORDER BY begin_date; 