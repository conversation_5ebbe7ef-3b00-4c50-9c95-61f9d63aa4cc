import datetime
import traceback
import numpy as np
import pandas as pd
from stock_centre import Stock_Data_Centre
import sys
import os
sys.path.append('/mnt/Data2/data_auto_download')
from utils import update_success, update_err, mysql_execute, mysql_save_df, mysql_create_table, mysql_save_df_with_comparison

CLASS_ = 'stock'
FUNC_ = 'stock_basic'

def get_stock_info():
    hq = Stock_Data_Centre()
    info_col = ['ts_code', 'symbol', 'name', 'area', 'industry',
                'fullname', 'enname', 'cnspell', 'market', 'exchange',
                'curr_type', 'list_status', 'list_date', 'delist_date', 'is_hs']
    _df1 = hq.get_ts_data("pro.stock_basic(exchange='', list_status='L', fields='{}')".format(",".join(info_col)))
    _df2 = hq.get_ts_data("pro.stock_basic(exchange='', list_status='D', fields='{}')".format(",".join(info_col)))
    _df3 = hq.get_ts_data("pro.stock_basic(exchange='', list_status='P', fields='{}')".format(",".join(info_col)))
    stock_info = pd.concat([_df1, _df2, _df3])
    stock_info['list_date'] = pd.to_datetime(stock_info['list_date'].astype(str))
    stock_info['delist_date'] = pd.to_datetime(stock_info['delist_date'])
    stock_info = stock_info.rename({'ts_code': 'code'}, axis=1)
    return stock_info

def updata_stock_basic():
    try:
        now = datetime.datetime.now()
        hq = Stock_Data_Centre(engine='mysql')
        mysql_create_table('stock_basic', 'stock/basic_data/SQL/stock_basic.sql')
        if now.hour < 15:
            now = now - datetime.timedelta(days=1)
        today = now.strftime('%Y%m%d')
        today_fmt = now.strftime('%Y-%m-%d')
        # 获取数据
        stock_info = get_stock_info()
        # 智能保存数据，比对变化
        print("保存数据到数据库，比对数据变化...")
        mysql_save_df_with_comparison(
            stock_info, 
            'stock_basic', 
            unique_columns=['code'],  # 以股票代码作为唯一标识
            index=False
        )
        update_success(today_fmt, CLASS_, FUNC_)
        print(f"{CLASS_}_{FUNC_}_{today_fmt}更新成功")
    except:
        traceback.print_exc(file=open(f'err_logs/{CLASS_}_{FUNC_}_{now.strftime("%Y%m%d")}.log', 'w+'))
        update_err(CLASS_, FUNC_)
        traceback.print_exc()

if __name__ == '__main__':
    updata_stock_basic()