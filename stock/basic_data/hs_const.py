import datetime
import traceback
import numpy as np
import pandas as pd
from stock_centre import Stock_Data_Centre
import sys
import os
sys.path.append('/mnt/Data2/data_auto_download')
from utils import update_success, update_err, mysql_execute, mysql_save_df, mysql_create_table, mysql_save_df_with_comparison

CLASS_ = 'stock'
FUNC_ = 'stock_hs_const'

def get_stock_hs_const_data():
    """
    获取沪深股通成份股数据
    
    Returns:
        pandas.DataFrame: 包含沪股通和深股通成份股数据的DataFrame
    """
    hq = Stock_Data_Centre()
    
    const_df_list = []
    for hs_type in ['SH', 'SZ']:
        for is_new in ['1', '0']:
            const_sql = f"pro.stock_hs_const(hs_type='{hs_type}', is_new='{is_new}')"
            const_df_list.append(hq.get_ts_data(const_sql))
    stock_hs_const_data = pd.concat(const_df_list, ignore_index=True)
    
    # 数据处理
    if not stock_hs_const_data.empty:
        # 处理日期字段
        stock_hs_const_data['in_date'] = pd.to_datetime(stock_hs_const_data['in_date'], format='%Y%m%d', errors='coerce')
        stock_hs_const_data['out_date'] = pd.to_datetime(stock_hs_const_data['out_date'], format='%Y%m%d', errors='coerce')
        
        # 重命名字段以匹配数据库表结构
        if 'ts_code' in stock_hs_const_data.columns:
            stock_hs_const_data = stock_hs_const_data.rename({'ts_code': 'code'}, axis=1)
        
        # 确保字段类型正确
        stock_hs_const_data['code'] = stock_hs_const_data['code'].astype(str)
        stock_hs_const_data['hs_type'] = stock_hs_const_data['hs_type'].astype(str)
        stock_hs_const_data['is_new'] = stock_hs_const_data['is_new'].astype(str)
        
        # 按照股票代码和类型排序
        stock_hs_const_data = stock_hs_const_data.sort_values(['hs_type', 'code', 'in_date'])
        stock_hs_const_data = stock_hs_const_data.reset_index(drop=True)
    
    return stock_hs_const_data

def update_stock_hs_const():
    """
    更新沪深股通成份股数据 - 全量覆盖式入库
    """
    try:
        now = datetime.datetime.now()
        hq = Stock_Data_Centre(engine='mysql')
        
        # 创建表结构
        mysql_create_table('stock_hs_const', 'stock/basic_data/SQL/stock_hs_const.sql')
        
        # 判断时间，如果是15点前，使用前一天日期
        if now.hour < 15:
            now = now - datetime.timedelta(days=1)
        today = now.strftime('%Y%m%d')
        today_fmt = now.strftime('%Y-%m-%d')
        
        print(f"开始更新沪深股通成份股数据: {today_fmt}")
        
        # 检查是否为交易日
        trade_date_test = hq.get_ts_data(f"pro.trade_cal(start_date='{today}', end_date='{today}', is_open=1)")
        if len(trade_date_test) == 0:
            print("非交易日，但仍执行更新（沪深股通成份股变化不依赖交易日）")
        
        # 全量删除旧数据
        print("删除现有数据...")
        mysql_execute("DELETE FROM stock_hs_const WHERE 1=1;")
        
        # 获取最新数据
        print("获取沪深股通成份股数据...")
        stock_hs_const_data = get_stock_hs_const_data()
        
        if not stock_hs_const_data.empty:
            # 保存数据到数据库（智能比对更新）
            print("保存数据到数据库，比对数据变化...")
            mysql_save_df_with_comparison(
                stock_hs_const_data, 
                'stock_hs_const', 
                unique_columns=['code', 'hs_type', 'in_date'],  # 以股票代码、类型和纳入日期作为唯一标识
                index=False
            )
            
            # 数据统计
            total_count = len(stock_hs_const_data)
            sh_count = len(stock_hs_const_data[stock_hs_const_data['hs_type'] == 'SH'])
            sz_count = len(stock_hs_const_data[stock_hs_const_data['hs_type'] == 'SZ'])
            current_count = len(stock_hs_const_data[stock_hs_const_data['is_new'] == '1'])
            
            print(f"数据入库完成:")
            print(f"  总记录数: {total_count}")
            print(f"  沪股通记录数: {sh_count}")
            print(f"  深股通记录数: {sz_count}")
            print(f"  当前有效成份股: {current_count}")
            
            # 更新成功状态
            update_success(today_fmt, CLASS_, FUNC_)
            print(f"{CLASS_}_{FUNC_}_{today_fmt}更新成功")
        else:
            print("未获取到有效数据")
            update_err(CLASS_, FUNC_)
            
    except Exception as e:
        error_msg = str(e)
        print(f"更新失败: {error_msg}")
        traceback.print_exc(file=open(f'err_logs/{CLASS_}_{FUNC_}_{now.strftime("%Y%m%d")}.log', 'w+'))
        update_err(CLASS_, FUNC_)
        traceback.print_exc()

if __name__ == '__main__':
    update_stock_hs_const()