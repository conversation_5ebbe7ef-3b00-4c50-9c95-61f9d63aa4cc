import datetime
import traceback
import numpy as np
import pandas as pd
from stock_centre import Stock_Data_Centre
import sys
import os
sys.path.append('/mnt/Data2/data_auto_download')
from utils import update_success, update_err, mysql_execute, mysql_save_df, mysql_create_table, mysql_save_df_with_comparison

CLASS_ = 'stock'
FUNC_ = 'trade_cal'


def get_trade_cal(exchange, start_date, end_date):
    hq = Stock_Data_Centre()
    trade_cal = hq.get_ts_data(
            f"pro.trade_cal(exchange='{exchange}', start_date='{start_date}', end_date='{end_date}')").sort_values(
            'cal_date')
    trade_cal['pre_date'] = trade_cal['pretrade_date']
    trade_cal = trade_cal.loc[:, ['cal_date', 'pre_date', 'is_open']]
    trade_cal['exchange'] = exchange  # 添加交易所字段
    trade_cal['cal_date'] = pd.to_datetime(trade_cal['cal_date'])
    trade_cal['pre_date'] = pd.to_datetime(trade_cal['pre_date'])
    trade_cal['is_open'] = trade_cal['is_open'].astype(int)
    trade_cal_close = trade_cal[trade_cal['is_open'] == 0]
    trade_cal_close['next_date'] = np.nan
    trade_cal_open = trade_cal[trade_cal['is_open'] == 1]
    trade_cal_open['next_date'] = trade_cal_open['cal_date'].shift(-1)
    trade_cal = pd.concat([trade_cal_open, trade_cal_close])
    trade_cal = trade_cal.sort_values('cal_date')
    trade_cal = trade_cal.reset_index(drop=True)
    trade_cal['next_date'] = trade_cal['next_date'].fillna(method='ffill')
    return trade_cal

def updata_trade_cal():
    try:
        now = datetime.datetime.now()
        hq = Stock_Data_Centre(engine='mysql')
        mysql_create_table('trade_cal', 'stock/basic_data/SQL/trade_cal.sql')
        if now.hour < 15:
            now = now - datetime.timedelta(days=1)
        today = now.strftime('%Y%m%d')
        today_fmt = now.strftime('%Y-%m-%d')
        trade_date_test = hq.get_ts_data(f"pro.trade_cal(start_date='{today}', end_date='{today}', is_open=1)")
        if len(trade_date_test) == 0:
            print("非交易日，无需更新行情")
            return
        # 交易日历更新
        last_updata = '20000101'
        # 到明年的12-31
        end_date = now.replace(year=now.year + 1, month=12, day=31).strftime('%Y%m%d')
        # hq.get_stock_data('select * from trade_cal order by cal_date desc limit 1').iloc[0].cal_date.strftime('%Y%m%d')
        # 交易所 SSE上交所,SZSE深交所,CFFEX 中金所,SHFE 上期所,CZCE 郑商所,DCE 大商所,INE 上能源
        trade_cal_list = []
        for exchange in ['SSE', 'SZSE', 'CFFEX', 'SHFE', 'CZCE', 'DCE', 'INE']:
            trade_cal = get_trade_cal(exchange, last_updata, end_date)
            trade_cal_list.append(trade_cal)
        trade_cal = pd.concat(trade_cal_list)
        trade_cal = trade_cal.sort_values('cal_date')
        trade_cal = trade_cal.reset_index(drop=True)

        # 智能保存数据，比对变化
        print("保存数据到数据库，比对数据变化...")
        mysql_save_df_with_comparison(
            trade_cal.drop_duplicates(), 
            'trade_cal', 
            unique_columns=['exchange', 'cal_date'],  # 以交易所和日期作为唯一标识
            index=False
        )
        update_success(today_fmt, CLASS_, FUNC_)
        print(f"{CLASS_}_{FUNC_}_{today_fmt}更新成功")
    except:
        traceback.print_exc(file=open(f'err_logs/{CLASS_}_{FUNC_}_{now.strftime("%Y%m%d")}.log', 'w+'))
        update_err(CLASS_, FUNC_)
        traceback.print_exc()

if __name__ == '__main__':
    updata_trade_cal()