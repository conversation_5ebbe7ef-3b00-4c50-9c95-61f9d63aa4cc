import datetime
import traceback
import numpy as np
import pandas as pd
from stock_centre import Stock_Data_Centre
import sys
import os
sys.path.append('/mnt/Data2/data_auto_download')
from utils import update_success, update_err, mysql_execute, mysql_save_df, mysql_create_table, mysql_save_df_with_comparison

CLASS_ = 'stock'
FUNC_ = 'stock_company'

def get_stock_company_data():
    """
    获取上市公司基本信息数据
    
    Returns:
        pandas.DataFrame: 包含上市公司基本信息的DataFrame
    """
    hq = Stock_Data_Centre()
    
    company_df_list = []
    # 按交易所分批获取，避免单次请求数据量过大
    exchanges = ['SSE', 'SZSE', 'BSE']  # 上交所、深交所、北交所
    
    for exchange in exchanges:
        company_sql = f"pro.stock_company(exchange='{exchange}')"
        exchange_data = hq.get_ts_data(company_sql)
        company_df_list.append(exchange_data)
    
    # 合并所有交易所数据
    company_data = pd.concat(company_df_list, ignore_index=True)
    
    # 数据处理
    if not company_data.empty:
        # 处理日期字段
        company_data['setup_date'] = pd.to_datetime(company_data['setup_date'], format='%Y%m%d', errors='coerce')
        
        # 重命名字段以匹配数据库表结构（如果需要）
        if 'ts_code' in company_data.columns:
            company_data = company_data.rename({'ts_code': 'code'}, axis=1)
        
        # 确保字符串字段类型正确，处理None值
        string_columns = ['code', 'com_name', 'com_id', 'exchange', 'chairman', 
                         'manager', 'secretary', 'province', 'city', 'introduction',
                         'website', 'email', 'office', 'main_business', 'business_scope']
        
        for col in string_columns:
            if col in company_data.columns:
                company_data[col] = company_data[col].astype(str)
                company_data[col] = company_data[col].replace('nan', '')
                company_data[col] = company_data[col].replace('None', '')
        
        # 处理数值字段
        if 'reg_capital' in company_data.columns:
            company_data['reg_capital'] = pd.to_numeric(company_data['reg_capital'], errors='coerce')
        
        if 'employees' in company_data.columns:
            company_data['employees'] = pd.to_numeric(company_data['employees'], errors='coerce')
        
        # 按照股票代码排序
        company_data = company_data.sort_values(['exchange', 'code'])
        company_data = company_data.reset_index(drop=True)

    return company_data

def update_stock_company():
    """
    更新上市公司基本信息数据 - 全量覆盖式入库
    """
    try:
        now = datetime.datetime.now()
        hq = Stock_Data_Centre(engine='mysql')
        
        # 创建表结构
        mysql_create_table('stock_company', 'stock/basic_data/SQL/stock_company.sql')
        
        # 判断时间，如果是15点前，使用前一天日期
        if now.hour < 15:
            now = now - datetime.timedelta(days=1)
        today = now.strftime('%Y%m%d')
        today_fmt = now.strftime('%Y-%m-%d')
        
        print(f"开始更新上市公司基本信息数据: {today_fmt}")
        
        # 检查是否为交易日
        trade_date_test = hq.get_ts_data(f"pro.trade_cal(start_date='{today}', end_date='{today}', is_open=1)")
        if len(trade_date_test) == 0:
            print("非交易日，但仍执行更新（上市公司基本信息变化不依赖交易日）")
        
        # 全量删除旧数据
        print("删除现有数据...")
        mysql_execute("DELETE FROM stock_company WHERE 1=1;")
        
        # 获取最新数据
        print("获取上市公司基本信息数据...")
        company_data = get_stock_company_data()
        
        if not company_data.empty:
            # 保存数据到数据库（智能比对更新）
            print("保存数据到数据库，比对数据变化...")
            mysql_save_df_with_comparison(
                company_data, 
                'stock_company', 
                unique_columns=['code'],  # 以股票代码作为唯一标识
                index=False
            )
            
            # 数据统计
            total_count = len(company_data)
            sse_count = len(company_data[company_data['exchange'] == 'SSE'])
            szse_count = len(company_data[company_data['exchange'] == 'SZSE'])
            bse_count = len(company_data[company_data['exchange'] == 'BSE'])
            
            # 更新成功状态
            update_success(today_fmt, CLASS_, FUNC_)
            print(f"{CLASS_}_{FUNC_}_{today_fmt}更新成功")
        else:
            print("未获取到有效数据")
            update_err(CLASS_, FUNC_)
            
    except Exception as e:
        error_msg = str(e)
        print(f"更新失败: {error_msg}")
        traceback.print_exc(file=open(f'err_logs/{CLASS_}_{FUNC_}_{now.strftime("%Y%m%d")}.log', 'w+'))
        update_err(CLASS_, FUNC_)
        traceback.print_exc()

if __name__ == '__main__':
    update_stock_company()
