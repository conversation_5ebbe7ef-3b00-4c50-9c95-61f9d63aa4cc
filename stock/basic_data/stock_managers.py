import datetime
import traceback
import numpy as np
import pandas as pd
from stock_centre import Stock_Data_Centre
import sys
import os
sys.path.append('/mnt/Data2/data_auto_download')
from utils import update_success, update_err, mysql_execute, mysql_save_df, mysql_create_table, mysql_read_data, mysql_save_df_with_comparison

CLASS_ = 'stock'
FUNC_ = 'stock_managers'

def get_stock_managers_data(ts_codes=None, ann_date=None, start_date=None, end_date=None):
    """
    获取上市公司管理层信息数据
    
    Args:
        ts_codes (str or list): 股票代码，支持单个或多个股票输入
        ann_date (str): 公告日期（YYYYMMDD格式）
        start_date (str): 公告开始日期
        end_date (str): 公告结束日期
    
    Returns:
        pandas.DataFrame: 包含上市公司管理层信息的DataFrame
    """
    hq = Stock_Data_Centre()
    
    # 构建查询参数
    params = []
    if ts_codes:
        if isinstance(ts_codes, list):
            ts_codes = ','.join(ts_codes)
        params.append(f"ts_code='{ts_codes}'")
    
    if ann_date:
        params.append(f"ann_date='{ann_date}'")
    
    if start_date:
        params.append(f"start_date='{start_date}'")
        
    if end_date:
        params.append(f"end_date='{end_date}'")
    
    # 构建查询语句
    param_str = ', '.join(params) if params else ''
    managers_sql = f"pro.stk_managers({param_str})"
    
    print(f"执行查询: {managers_sql}")
    managers_data = hq.get_ts_data(managers_sql)
    
    # 数据处理
    if not managers_data.empty:
        # 重命名ts_code字段为code以匹配数据库表结构
        if 'ts_code' in managers_data.columns:
            managers_data = managers_data.rename({'ts_code': 'code'}, axis=1)
        
        # 确保字符串字段类型正确，处理None值
        string_columns = ['code', 'ann_date', 'name', 'gender', 'lev', 'title', 
                         'edu', 'national', 'birthday', 'begin_date', 'end_date']
        
        for col in string_columns:
            if col in managers_data.columns:
                managers_data[col] = managers_data[col].astype(str)
                managers_data[col] = managers_data[col].replace('nan', '')
                managers_data[col] = managers_data[col].replace('None', '')
        
        # 处理resume字段（个人简历）
        if 'resume' in managers_data.columns:
            managers_data['resume'] = managers_data['resume'].astype(str)
            managers_data['resume'] = managers_data['resume'].replace('nan', '')
            managers_data['resume'] = managers_data['resume'].replace('None', '')
        
        # 按code, ann_date, name排序
        managers_data = managers_data.sort_values(['code', 'ann_date', 'name'])
        managers_data = managers_data.reset_index(drop=True)
    
    return managers_data

def get_existing_max_date():
    """
    获取数据库中已存在数据的最大公告日期，用于增量更新
    
    Returns:
        str: 最大公告日期（YYYYMMDD格式），如果表为空则返回None
    """
    try:
        sql = "SELECT MAX(ann_date) as max_date FROM stock_managers"
        result = mysql_read_data(sql)
        
        if not result.empty and result.iloc[0]['max_date'] is not None:
            max_date = str(result.iloc[0]['max_date'])
            # 确保返回8位日期格式
            if len(max_date) == 8 and max_date.isdigit():
                return max_date
        
        return None
    except Exception as e:
        print(f"获取最大公告日期失败: {e}")
        return None

def update_stock_managers(incremental=True):
    """
    更新上市公司管理层信息数据
    
    Args:
        incremental (bool): 是否增量更新，True为增量更新，False为全量更新
    """
    today = datetime.date.today().strftime('%Y-%m-%d')
    print(f"开始更新上市公司管理层信息数据: {today}")
    
    try:
        # 创建表（如果不存在）
        print("创建数据库表...")
        sql_file = 'stock/basic_data/SQL/stock_managers.sql'
        mysql_create_table('stock_managers', sql_file)
        
        # 确定更新策略和时间范围
        if incremental:
            max_date = get_existing_max_date()
            if max_date:
                # 从最大日期所在月份开始更新（包含最大日期当月，因为可能有增量数据）
                max_year = int(max_date[:4])
                max_month = int(max_date[4:6])
                
                start_year = max_year
                start_month = max_month
                    
                print(f"增量更新模式，从 {start_year}年{start_month}月 开始（从 {max_date} 开始可能有增量数据）")
            else:
                print("数据库为空，从2018年开始全量更新")
                start_year = 2018
                start_month = 1
        else:
            print("全量更新模式，从2018年开始")
            # 删除现有数据
            print("删除现有数据...")
            mysql_execute("TRUNCATE TABLE stock_managers")
            start_year = 2018
            start_month = 1
        
        # 当前时间
        current_date = datetime.date.today()
        current_year = current_date.year
        current_month = current_date.month
        
        total_records = 0
        
        # 逐月处理数据
        year = start_year
        month = start_month
        
        while year < current_year or (year == current_year and month <= current_month):
            # 计算当月的开始和结束日期
            start_date = f"{year:04d}{month:02d}01"
            
            # 计算当月最后一天
            if month == 12:
                next_month = 1
                next_year = year + 1
            else:
                next_month = month + 1
                next_year = year
            
            last_day = (datetime.date(next_year, next_month, 1) - datetime.timedelta(days=1)).day
            end_date = f"{year:04d}{month:02d}{last_day:02d}"
            
            print(f"处理 {year}年{month}月 数据 ({start_date} - {end_date})")
            
            try:
                # 获取当月所有股票的管理层数据
                managers_data = get_stock_managers_data(
                    start_date=start_date,
                    end_date=end_date
                )
                
                if not managers_data.empty:
                    print(f"获取到 {len(managers_data)} 条管理层数据")
                    
                    # 如果是增量模式，先删除当月的数据，再插入新数据
                    if incremental:
                        delete_sql = f"""
                        DELETE FROM stock_managers 
                        WHERE ann_date >= '{start_date}' AND ann_date <= '{end_date}'
                        """
                        mysql_execute(delete_sql)
                        print(f"删除了 {start_date} 到 {end_date} 的旧数据")
                    
                    # 为新获取的数据添加更新时间
                    from datetime import datetime
                    managers_data['last_update_time'] = datetime.now()
                    
                    # 插入新数据
                    mysql_save_df(managers_data, 'stock_managers', if_exists='append', index=False)
                    total_records += len(managers_data)
                    print(f"月度数据保存完成，累计 {total_records} 条记录")
                else:
                    print(f"{year}年{month}月 未获取到数据")
                    
            except Exception as e:
                print(f"处理 {year}年{month}月 数据时出错: {e}")
                traceback.print_exc()
                # 继续处理下个月
            
            # 移动到下个月
            if month == 12:
                year += 1
                month = 1
            else:
                month += 1
        
        # 更新统计信息
        print("获取最终统计信息...")
        stats_sql = """
        SELECT 
            COUNT(*) as total_count,
            COUNT(DISTINCT code) as stock_count,
            COUNT(DISTINCT name) as manager_count,
            MIN(ann_date) as min_date,
            MAX(ann_date) as max_date
        FROM stock_managers
        """
        stats = mysql_read_data(stats_sql)
        
        if not stats.empty:
            stats_row = stats.iloc[0]
            print(f"数据入库完成:")
            print(f"  总记录数: {stats_row['total_count']}")
            print(f"  涉及股票数: {stats_row['stock_count']}")
            print(f"  管理层人数: {stats_row['manager_count']}")
            print(f"  数据日期范围: {stats_row['min_date']} - {stats_row['max_date']}")
        
        # 更新成功状态
        table_name = f"{CLASS_}_{FUNC_}"
        update_success(today, CLASS_, FUNC_)
        print(f"{table_name}更新成功")
        
    except Exception as e:
        error_msg = str(e)
        print(f"更新失败: {error_msg}")
        traceback.print_exc(file=open(f'err_logs/{CLASS_}_{FUNC_}_{today.replace("-", "")}.log', 'w+'))
        update_err(CLASS_, FUNC_)
        traceback.print_exc()


if __name__ == '__main__':
    # 默认执行增量更新
    update_stock_managers(incremental=True)
    
    # 测试查询功能
    print("\n" + "="*50)
    print("测试查询功能:")
    
    # # 测试获取某公司管理层信息
    # test_code = '000001.SZ'
    # managers = get_managers_by_code(test_code)
    # print(f"\n{test_code} 管理层信息数量: {len(managers)}")
    
    # if not managers.empty:
    #     print("最新管理层信息:")
    #     latest_managers = managers[managers['ann_date'] == managers['ann_date'].max()]
    #     for _, row in latest_managers.head(3).iterrows():
    #         print(f"  {row['name']} - {row['title']} (公告日期: {row['ann_date']})")
    
    # # 测试获取现任管理层
    # current_managers = get_current_managers(test_code)
    # print(f"\n{test_code} 现任管理层数量: {len(current_managers)}")
    
    # if not current_managers.empty:
    #     for _, row in current_managers.head(3).iterrows():
    #         print(f"  {row['name']} - {row['title']} (上任日期: {row['begin_date']})") 