import datetime
import traceback
import pandas as pd
from stock_centre import Stock_Data_Centre
import sys
import os
sys.path.append('/mnt/Data2/data_auto_download')
from utils import update_success, update_err, mysql_execute, mysql_save_df, mysql_create_table, mysql_read_data, mysql_save_df_with_comparison

CLASS_ = 'stock'
FUNC_ = 'stock_namechange'


def get_stock_namechange(ts_code: str | None = None, start_date: str | None = None, end_date: str | None = None) -> pd.DataFrame:
    """从 Tushare 拉取股票曾用名（名称变更记录）。

    参数均为可选，若全部为空则全量获取。
    - ts_code: 如 '600848.SH'
    - start_date/end_date: 'YYYYMMDD'
    """
    hq = Stock_Data_Centre()
    query_parts = ["pro.stock_namechange("]
    if ts_code is not None:
        query_parts.append(f"ts_code='{ts_code}', ")
    if start_date is not None:
        query_parts.append(f"start_date='{start_date}', ")
    if end_date is not None:
        query_parts.append(f"end_date='{end_date}', ")
    query = ''.join(query_parts).rstrip(', ') + ")"

    df = hq.get_ts_data(query)
    if df is None or len(df) == 0:
        return pd.DataFrame(columns=['code', 'name', 'start_date', 'end_date', 'ann_date', 'change_reason'])

    # 字段对齐并转型
    df = df.loc[:, ['ts_code', 'name', 'start_date', 'end_date', 'ann_date', 'change_reason']]
    df = df.rename(columns={'ts_code': 'code'})

    # 日期转为 DATE
    for col in ['start_date', 'end_date', 'ann_date']:
        if col in df.columns:
            df[col] = pd.to_datetime(df[col].astype(str), errors='coerce')

    # 去重
    df = df.drop_duplicates(subset=['code', 'start_date', 'name'])
    df = df.reset_index(drop=True)
    return df


def updata_stock_namechange():
    try:
        now = datetime.datetime.now()
        hq = Stock_Data_Centre(engine='mysql')
        mysql_create_table('stock_namechange', 'stock/basic_data/SQL/stock_namechange.sql')

        if now.hour < 15:
            now = now - datetime.timedelta(days=1)
        today_fmt = now.strftime('%Y-%m-%d')

        # 增量起始：取表内 max(start_date, ann_date) 的次日；若表空或无数据则从 2000-01-01 开始
        since_date = datetime.date(2000, 1, 1)
        try:
            max_df = mysql_read_data("SELECT MAX(start_date) AS max_start, MAX(ann_date) AS max_ann FROM stock_namechange")
            if max_df is not None and len(max_df) > 0:
                max_start = max_df.iloc[0]['max_start']
                max_ann = max_df.iloc[0]['max_ann']
                candidates = []
                if pd.notna(max_start) and max_start is not None:
                    # MySQL 返回为 datetime.date/datetime 或 pandas Timestamp
                    candidates.append(pd.to_datetime(max_start).date())
                if pd.notna(max_ann) and max_ann is not None:
                    candidates.append(pd.to_datetime(max_ann).date())
                if len(candidates) > 0:
                    since_date = max(candidates) + datetime.timedelta(days=1)
        except Exception as e:
            # 表不存在或查询失败，使用默认起始日期
            print(f"查询历史数据失败，使用默认起始日期 {since_date}: {e}")
            pass

        end_date_all = now.date()

        # 若已最新，直接记录成功
        if since_date > end_date_all:
            update_success(today_fmt, CLASS_, FUNC_)
            print(f"{CLASS_}_{FUNC_}_{today_fmt}已是最新，无需更新")
            return

        current = since_date
        while current <= end_date_all:
            window_end = min(current + datetime.timedelta(days=365) - datetime.timedelta(days=1), end_date_all)

            sd = current.strftime('%Y%m%d')
            ed = window_end.strftime('%Y%m%d')
            sd_date = current.strftime('%Y-%m-%d')
            ed_date = window_end.strftime('%Y-%m-%d')

            # 先清理该窗口内旧数据以确保幂等
            del_sql = (
                "DELETE FROM stock_namechange "
                f"WHERE (ann_date >= '{sd_date}' AND ann_date <= '{ed_date}') "
                f"OR (start_date >= '{sd_date}' AND start_date <= '{ed_date}');"
            )
            mysql_execute(del_sql)

            df = get_stock_namechange(start_date=sd, end_date=ed)
            if len(df) > 0:
                # 为新获取的数据添加更新时间
                from datetime import datetime
                df['last_update_time'] = datetime.now()
                mysql_save_df(df, 'stock_namechange', if_exists='append', index=False)

            current = window_end + datetime.timedelta(days=1)

        update_success(today_fmt, CLASS_, FUNC_)
        print(f"{CLASS_}_{FUNC_}_{today_fmt}更新成功")
    except Exception:
        traceback.print_exc(file=open(f'err_logs/{CLASS_}_{FUNC_}_{datetime.datetime.now().strftime("%Y%m%d")}.log', 'w+'))
        update_err(CLASS_, FUNC_)
        traceback.print_exc()


if __name__ == '__main__':
    updata_stock_namechange()
