# 数据更新时间功能说明

## 📖 功能概述

本功能为所有数据更新程序添加了智能的更新时间管理，主要特性包括：

1. **自动记录更新时间**：每条数据都会记录最后更新时间
2. **智能数据比对**：在全量更新时，只有数据发生变化的记录才会更新时间
3. **保留原始时间**：如果数据没有变化，则保留原来的更新时间
4. **统一的接口**：所有更新程序都使用相同的更新时间管理机制

## 🛠️ 技术实现

### 数据库结构变更

为以下表添加了 `last_update_time` 字段：

- `stock_company` - 上市公司基本信息
- `stock_basic` - 股票基本信息  
- `stock_hs_const` - 沪深港通成分股
- `trade_cal` - 交易日历
- `stock_namechange` - 股票名称变更记录
- `stock_managers` - 上市公司管理层信息

字段定义：
```sql
last_update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间'
```

### 核心功能

#### 1. 智能数据比对函数 (`mysql_save_df_with_comparison`)

```python
def mysql_save_df_with_comparison(df, table_name, unique_columns, compare_columns=None, index=False):
    """
    智能保存DataFrame到MySQL，在全量更新时比对数据变动
    如果数据没有变动，则保留原来的更新时间
    
    参数:
    - df: 要保存的DataFrame
    - table_name: 表名
    - unique_columns: 用于确定唯一记录的列名列表
    - compare_columns: 用于比对变化的列名列表，如果为None则比对除unique_columns和last_update_time之外的所有列
    - index: 是否保存索引
    """
```

#### 2. 更新时间统计函数

- `get_table_update_stats(table_name)` - 获取表的更新时间统计信息
- `print_table_update_info(table_name)` - 打印表的更新时间信息

## 📋 使用指南

### 1. 数据库迁移

首次使用需要运行迁移脚本为现有表添加更新时间字段：

```bash
python migrate_add_update_time.py
```

### 2. 功能测试

运行测试脚本验证功能是否正常：

```bash
python test_update_time.py
```

### 3. 更新程序的使用

各个更新程序已经自动集成了更新时间功能：

#### 全量更新程序（使用智能比对）

- `stock_company.py` - 上市公司基本信息
- `stock_basic.py` - 股票基本信息
- `hs_const.py` - 沪深港通成分股
- `trade_cal.py` - 交易日历

这些程序使用 `mysql_save_df_with_comparison` 函数，会自动：
- 比对新旧数据
- 只为发生变化的记录更新时间
- 保留未变化记录的原始更新时间

#### 增量更新程序

- `namechange.py` - 股票名称变更记录
- `stock_managers.py` - 上市公司管理层信息

这些程序为新获取的数据自动添加当前时间作为更新时间。

## 🔍 数据比对逻辑

### 唯一标识字段配置

| 表名 | 唯一标识字段 | 说明 |
|------|-------------|------|
| stock_company | `['code']` | 股票代码 |
| stock_basic | `['code']` | 股票代码 |
| stock_hs_const | `['code', 'hs_type', 'in_date']` | 股票代码+类型+纳入日期 |
| trade_cal | `['exchange', 'cal_date']` | 交易所+日期 |

### 比对算法

1. **获取现有数据**：从数据库读取当前所有记录
2. **创建唯一键**：基于唯一标识字段生成唯一键
3. **逐条比对**：
   - 找到对应的旧记录
   - 比较所有业务字段（除唯一标识和更新时间外）
   - 处理 NULL 值比较
4. **时间决策**：
   - 数据有变化：使用当前时间
   - 数据无变化：保留原更新时间
   - 新增记录：使用当前时间

## 📊 监控和统计

### 查看更新时间统计

```python
from utils import print_table_update_info

# 查看单个表的更新时间信息
print_table_update_info('stock_company')
```

输出示例：
```
📊 表 stock_company 更新时间统计:
==================================================
📈 总记录数: 5234
🕐 最早更新时间: 2024-01-15 10:30:00
🕐 最新更新时间: 2024-01-20 15:45:30
📅 总更新天数: 5

📅 近期每日更新记录数:
  2024-01-20: 123 条记录
  2024-01-19: 45 条记录
  2024-01-18: 0 条记录
  2024-01-17: 89 条记录
  2024-01-16: 67 条记录
==================================================
```

## 🚀 性能优化

### 比对性能优化策略

1. **批量操作**：使用 DataFrame 进行批量数据处理
2. **索引优化**：基于唯一标识字段创建临时索引
3. **内存管理**：大数据量时分批处理
4. **早期退出**：发现第一个差异即停止比对

### 建议的使用场景

- **全量更新**：数据变化不频繁的基础信息表
- **增量更新**：数据变化频繁的实时数据表
- **混合模式**：根据数据特性选择合适的更新策略

## ⚠️ 注意事项

1. **首次运行**：需要先执行数据库迁移脚本
2. **性能考虑**：大表全量更新时比对可能较慢，建议在低峰期运行
3. **数据一致性**：比对过程中避免并发修改数据
4. **备份**：重要数据更新前建议先备份

## 🐛 故障排除

### 常见问题

1. **字段不存在错误**
   - 原因：未运行迁移脚本
   - 解决：执行 `python migrate_add_update_time.py`

2. **比对性能慢**
   - 原因：数据量大或缺少索引
   - 解决：为唯一标识字段添加索引

3. **时间格式错误**
   - 原因：数据库时区设置问题
   - 解决：检查MySQL时区配置

### 调试方法

1. 开启详细日志输出
2. 使用测试脚本验证功能
3. 查看错误日志文件

## 📝 版本历史

- **v1.0.0** (2024-01-20)
  - 初始版本
  - 实现基本的更新时间功能
  - 支持智能数据比对
  - 添加统计和监控功能 