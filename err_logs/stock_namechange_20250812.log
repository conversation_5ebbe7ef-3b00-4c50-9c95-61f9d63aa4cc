Traceback (most recent call last):
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1936, in _exec_single_context
    self.dialect.do_executemany(
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/sqlalchemy/dialects/mysql/mysqldb.py", line 196, in do_executemany
    rowcount = cursor.executemany(statement, parameters)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/cursors.py", line 182, in executemany
    return self._do_execute_many(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/cursors.py", line 220, in _do_execute_many
    rows += self.execute(sql + postfix)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.IntegrityError: (1062, "Duplicate entry '002143.SZ-2019-10-18' for key 'namechange.uk_code_start_date'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/Data2/data_auto_download/stock/basic_data/namechange.py", line 107, in updata_namechange
    mysql_save_df(df, 'namechange', if_exists='append', index=False)
  File "/mnt/Data2/data_auto_download/utils.py", line 33, in mysql_save_df
    df.to_sql(table_name, conn, if_exists=if_exists, index=index)
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pandas/util/_decorators.py", line 333, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pandas/core/generic.py", line 3106, in to_sql
    return sql.to_sql(
           ^^^^^^^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pandas/io/sql.py", line 844, in to_sql
    return pandas_sql.to_sql(
           ^^^^^^^^^^^^^^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pandas/io/sql.py", line 2030, in to_sql
    total_inserted = sql_engine.insert_records(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pandas/io/sql.py", line 1579, in insert_records
    raise err
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pandas/io/sql.py", line 1570, in insert_records
    return table.insert(chunksize=chunksize, method=method)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pandas/io/sql.py", line 1121, in insert
    num_inserted = exec_insert(conn, keys, chunk_iter)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pandas/io/sql.py", line 1012, in _execute_insert
    result = conn.execute(self.table.insert(), data)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1419, in execute
    return meth(
           ^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/sqlalchemy/sql/elements.py", line 526, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1641, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1846, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 2355, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1936, in _exec_single_context
    self.dialect.do_executemany(
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/sqlalchemy/dialects/mysql/mysqldb.py", line 196, in do_executemany
    rowcount = cursor.executemany(statement, parameters)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/cursors.py", line 182, in executemany
    return self._do_execute_many(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/cursors.py", line 220, in _do_execute_many
    rows += self.execute(sql + postfix)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.IntegrityError: (pymysql.err.IntegrityError) (1062, "Duplicate entry '002143.SZ-2019-10-18' for key 'namechange.uk_code_start_date'")
[SQL: INSERT INTO namechange (code, name, start_date, end_date, ann_date, change_reason) VALUES (%(code)s, %(name)s, %(start_date)s, %(end_date)s, %(ann_date)s, %(change_reason)s)]
[parameters: [{'code': '600732.SH', 'name': 'ST爱旭', 'start_date': datetime.datetime(2019, 12, 27, 0, 0), 'end_date': datetime.datetime(2020, 3, 4, 0, 0), 'ann_date': datetime.datetime(2019, 12, 24, 0, 0), 'change_reason': '改名'}, {'code': '600060.SH', 'name': '海信视像', 'start_date': datetime.datetime(2019, 12, 26, 0, 0), 'end_date': None, 'ann_date': datetime.datetime(2019, 12, 21, 0, 0), 'change_reason': '改名'}, {'code': '600290.SH', 'name': 'ST华仪', 'start_date': datetime.datetime(2019, 12, 26, 0, 0), 'end_date': datetime.datetime(2020, 4, 26, 0, 0), 'ann_date': datetime.datetime(2019, 12, 25, 0, 0), 'change_reason': 'ST'}, {'code': '000785.SZ', 'name': '居然之家', 'start_date': datetime.datetime(2019, 12, 26, 0, 0), 'end_date': None, 'ann_date': datetime.datetime(2019, 12, 26, 0, 0), 'change_reason': '改名'}, {'code': '000796.SZ', 'name': '凯撒旅业', 'start_date': datetime.datetime(2019, 12, 25, 0, 0), 'end_date': None, 'ann_date': datetime.datetime(2019, 12, 25, 0, 0), 'change_reason': '改名'}, {'code': '600423.SH', 'name': 'ST柳化', 'start_date': datetime.datetime(2019, 12, 20, 0, 0), 'end_date': None, 'ann_date': datetime.datetime(2019, 12, 19, 0, 0), 'change_reason': '摘星'}, {'code': '300470.SZ', 'name': '中密控股', 'start_date': datetime.datetime(2019, 12, 17, 0, 0), 'end_date': None, 'ann_date': datetime.datetime(2019, 12, 17, 0, 0), 'change_reason': '改名'}, {'code': '000043.SZ', 'name': '招商积余', 'start_date': datetime.datetime(2019, 12, 16, 0, 0), 'end_date': None, 'ann_date': datetime.datetime(2019, 12, 6, 0, 0), 'change_reason': '改名'}  ... displaying 10 of 241 total bound parameter sets ...  {'code': '000553.SZ', 'name': '安道麦A', 'start_date': datetime.datetime(2019, 1, 10, 0, 0), 'end_date': None, 'ann_date': datetime.datetime(2019, 1, 10, 0, 0), 'change_reason': '改名'}, {'code': '601975.SH', 'name': 'ST长油', 'start_date': datetime.datetime(2019, 1, 8, 0, 0), 'end_date': datetime.datetime(2019, 3, 28, 0, 0), 'ann_date': datetime.datetime(2018, 12, 28, 0, 0), 'change_reason': 'ST'}]]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
