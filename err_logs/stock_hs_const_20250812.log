Traceback (most recent call last):
  File "/mnt/Data2/data_auto_download/stock/basic_data/hs_const.py", line 62, in update_hs_const
    mysql_create_table('hs_const', 'stock/basic_data/SQL/hs_const.sql')
  File "/mnt/Data2/data_auto_download/utils.py", line 118, in mysql_create_table
    raise e
  File "/mnt/Data2/data_auto_download/utils.py", line 112, in mysql_create_table
    cursor.execute(sql_stmt)
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/root/anaconda3/envs/data_download/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'CREATE OR REPLACE VIEW `v_hs_const_sh_current` AS\nSELECT\n`code`,\n`in_date`,\n`out' at line 12")
