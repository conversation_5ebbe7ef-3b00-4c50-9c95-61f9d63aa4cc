#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新时间功能
"""

import sys
import os
sys.path.append('/mnt/Data2/data_auto_download')

from utils import print_table_update_info, get_table_update_stats

def test_all_tables():
    """测试所有表的更新时间功能"""
    
    tables = [
        'stock_company',
        'stock_basic', 
        'stock_hs_const',
        'trade_cal',
        'stock_namechange',
        'stock_managers'
    ]
    
    print("🔍 检查所有表的更新时间字段功能...")
    print("=" * 60)
    
    for table in tables:
        print_table_update_info(table)
        print()

def check_table_structure():
    """检查表结构中是否正确添加了更新时间字段"""
    from utils import mysql_read_data
    
    tables = [
        'stock_company',
        'stock_basic', 
        'stock_hs_const',
        'trade_cal',
        'stock_namechange',
        'stock_managers'
    ]
    
    print("🏗️  检查表结构中的更新时间字段...")
    print("=" * 60)
    
    for table in tables:
        try:
            sql = f"""
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, EXTRA
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = '{table}' 
            AND COLUMN_NAME = 'last_update_time'
            """
            
            result = mysql_read_data(sql)
            
            if not result.empty:
                print(f"✅ 表 {table} 已正确添加 last_update_time 字段")
                print(f"   数据类型: {result.iloc[0]['DATA_TYPE']}")
                print(f"   默认值: {result.iloc[0]['COLUMN_DEFAULT']}")
                print(f"   额外属性: {result.iloc[0]['EXTRA']}")
            else:
                print(f"❌ 表 {table} 缺少 last_update_time 字段")
                
        except Exception as e:
            print(f"❌ 检查表 {table} 时出错: {e}")
        
        print()

if __name__ == '__main__':
    print("🚀 开始测试更新时间功能\n")
    
    # 检查表结构
    check_table_structure()
    
    # 检查更新时间统计
    test_all_tables()
    
    print("✨ 测试完成!") 