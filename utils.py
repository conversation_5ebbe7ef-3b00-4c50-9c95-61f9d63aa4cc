import os
from datetime import datetime
from sqlalchemy import create_engine
import setting
import pymysql
import pandas as pd

user = setting.mysql_config["user"]
password = setting.mysql_config["password"]
host = setting.mysql_config["host"]
port = setting.mysql_config["port"]
database = setting.mysql_config["database"]

def update_success(today_fmt, db_name, table_name):
    svae_dir = f'update_logs/{db_name}'
    if not os.path.exists(svae_dir):
        os.makedirs(svae_dir)
    with open(f'{svae_dir}/{table_name}.txt', 'w') as f:
        f.write(f'{today_fmt}|SUCCESS')

def update_err(db_name, table_name):
    svae_dir = f'update_logs/{db_name}'
    if not os.path.exists(svae_dir):
        os.makedirs(svae_dir)
    today_fmt = datetime.now().strftime('%Y-%m-%d')
    with open(f'{svae_dir}/{table_name}.txt', 'w') as f:
        f.write(f'{today_fmt}|ERROR')

def mysql_save_df(df, table_name, if_exists='append', index=False):
    engine = create_engine(f'mysql+mysqldb://{user}:{password}@{host}:{port}/{database}?charset=utf8')
    # 使用 engine.connect() 上下文管理器，这是 pandas 官方文档推荐的方式
    with engine.connect() as conn:
        df.to_sql(table_name, conn, if_exists=if_exists, index=index)

def mysql_save_df_with_comparison(df, table_name, unique_columns, compare_columns=None, index=False):
    """
    智能保存DataFrame到MySQL，在全量更新时比对数据变动
    如果数据没有变动，则保留原来的更新时间
    
    参数:
    - df: 要保存的DataFrame
    - table_name: 表名
    - unique_columns: 用于确定唯一记录的列名列表
    - compare_columns: 用于比对变化的列名列表，如果为None则比对除unique_columns和last_update_time之外的所有列
    - index: 是否保存索引
    """
    import pandas as pd
    from datetime import datetime
    
    current_time = datetime.now()
    
    # 如果DataFrame为空，直接返回
    if df.empty:
        return
        
    # 添加更新时间字段
    if 'last_update_time' not in df.columns:
        df['last_update_time'] = current_time
    
    # 检查表是否存在以及是否有数据
    try:
        existing_data = mysql_read_data(f"SELECT * FROM {table_name} LIMIT 1")
        has_existing_data = len(existing_data) > 0
    except:
        has_existing_data = False
    
    if not has_existing_data:
        # 表为空或不存在，直接插入所有数据
        mysql_save_df(df, table_name, if_exists='append', index=index)
        return
    
    # 获取现有数据
    existing_data = mysql_read_data(f"SELECT * FROM {table_name}")
    
    if existing_data.empty:
        # 现有数据为空，直接插入
        mysql_save_df(df, table_name, if_exists='append', index=index)
        return
    
    # 确定要比对的列
    if compare_columns is None:
        compare_columns = [col for col in df.columns if col not in unique_columns and col != 'last_update_time']
    
    # 为新数据创建唯一键
    df['_unique_key'] = df[unique_columns].astype(str).agg('|'.join, axis=1)
    existing_data['_unique_key'] = existing_data[unique_columns].astype(str).agg('|'.join, axis=1)
    
    # 创建结果DataFrame
    result_df = df.copy()
    
    # 对每条新记录检查是否存在对应的旧记录
    for idx, new_row in df.iterrows():
        unique_key = new_row['_unique_key']
        existing_row = existing_data[existing_data['_unique_key'] == unique_key]
        
        if not existing_row.empty:
            existing_row = existing_row.iloc[0]
            
            # 比对数据是否有变化
            has_changes = False
            for col in compare_columns:
                if col in existing_row and col in new_row:
                    # 处理NaN值比较
                    if pd.isna(existing_row[col]) and pd.isna(new_row[col]):
                        continue
                    elif pd.isna(existing_row[col]) or pd.isna(new_row[col]):
                        has_changes = True
                        break
                    elif str(existing_row[col]) != str(new_row[col]):
                        has_changes = True
                        break
            
            # 如果数据没有变化，保留原来的更新时间
            if not has_changes and 'last_update_time' in existing_row:
                result_df.loc[idx, 'last_update_time'] = existing_row['last_update_time']
    
    # 删除临时的唯一键列
    result_df = result_df.drop('_unique_key', axis=1)
    
    # 清空表并插入新数据
    mysql_execute(f"DELETE FROM {table_name} WHERE 1=1")
    mysql_save_df(result_df, table_name, if_exists='append', index=index)

def mysql_read_data(query):
    engine = create_engine(f'mysql+mysqldb://{user}:{password}@{host}:{port}/{database}?charset=utf8')
    with engine.connect() as conn:
        df = pd.read_sql_query(sql=query, con=conn)
        return df

def mysql_execute(sql):
    db = pymysql.connect(**setting.mysql_config)
    cursor = db.cursor()
    cursor.execute(sql)
    cursor.execute(sql)
    db.commit()
    cursor.close()
    db.close()

def mysql_create_table(table_name, sql_file):
    db = pymysql.connect(**setting.mysql_config)
    cursor = db.cursor()
    cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
    if cursor.fetchone():
        cursor.close()
        db.close()
        return
    
    with open(sql_file, 'r', encoding='utf-8') as f:
        sql_content = f.read()
    
    # 处理SQL内容，保留多行字符串
    lines = sql_content.split('\n')
    processed_lines = []
    in_multiline_string = False
    
    for line in lines:
        stripped_line = line.strip()
        
        # 跳过空行和注释行（除非在多行字符串中）
        if not in_multiline_string and (not stripped_line or stripped_line.startswith('--')):
            continue
            
        # 检查是否进入或退出多行字符串
        if not in_multiline_string and ('=' in stripped_line and "'" in stripped_line):
            in_multiline_string = True
        elif in_multiline_string and stripped_line.endswith("';"):
            in_multiline_string = False
            
        processed_lines.append(line)
    
    # 重新组合SQL内容
    sql_content = '\n'.join(processed_lines)
    
    # 更智能地分割SQL语句
    sql_statements = []
    current_statement = []
    in_string = False
    
    for line in sql_content.split('\n'):
        line = line.strip()
        if not line:
            continue
            
        current_statement.append(line)
        
        # 检查是否在字符串中
        if "'" in line:
            in_string = not in_string
            
        # 如果不在字符串中且行以分号结尾，则认为是语句结束
        if not in_string and line.endswith(';'):
            stmt = '\n'.join(current_statement)
            if stmt.strip():
                sql_statements.append(stmt)
            current_statement = []
    
    try:
        # 逐个执行SQL语句
        for sql_stmt in sql_statements:
            if sql_stmt.strip():
                cursor.execute(sql_stmt)
        db.commit()
    except Exception as e:
        db.rollback()
        cursor.close()
        db.close()
        raise e
    
    cursor.close()
    db.close()

def get_table_update_stats(table_name):
    """
    获取表的更新时间统计信息
    
    参数:
    - table_name: 表名
    
    返回:
    - dict: 包含更新统计信息的字典
    """
    try:
        # 检查表是否存在last_update_time字段
        check_column_sql = f"""
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = '{database}' 
        AND TABLE_NAME = '{table_name}' 
        AND COLUMN_NAME = 'last_update_time'
        """
        
        column_check = mysql_read_data(check_column_sql)
        if column_check.empty:
            return {"error": f"表 {table_name} 没有 last_update_time 字段"}
        
        # 获取更新时间统计
        stats_sql = f"""
        SELECT 
            COUNT(*) as total_records,
            MIN(last_update_time) as earliest_update,
            MAX(last_update_time) as latest_update,
            COUNT(DISTINCT DATE(last_update_time)) as update_days,
            DATE(last_update_time) as update_date,
            COUNT(*) as records_per_day
        FROM {table_name}
        WHERE last_update_time IS NOT NULL
        GROUP BY DATE(last_update_time)
        ORDER BY update_date DESC
        LIMIT 10
        """
        
        daily_stats = mysql_read_data(stats_sql)
        
        # 获取总体统计
        overall_sql = f"""
        SELECT 
            COUNT(*) as total_records,
            MIN(last_update_time) as earliest_update,
            MAX(last_update_time) as latest_update,
            COUNT(DISTINCT DATE(last_update_time)) as total_update_days
        FROM {table_name}
        WHERE last_update_time IS NOT NULL
        """
        
        overall_stats = mysql_read_data(overall_sql)
        
        return {
            "table_name": table_name,
            "overall_stats": overall_stats.to_dict('records')[0] if not overall_stats.empty else {},
            "daily_stats": daily_stats.to_dict('records') if not daily_stats.empty else []
        }
        
    except Exception as e:
        return {"error": str(e)}

def print_table_update_info(table_name):
    """
    打印表的更新时间信息
    
    参数:
    - table_name: 表名
    """
    stats = get_table_update_stats(table_name)
    
    if "error" in stats:
        print(f"❌ 获取表 {table_name} 更新信息失败: {stats['error']}")
        return
    
    print(f"\n📊 表 {table_name} 更新时间统计:")
    print("=" * 50)
    
    overall = stats["overall_stats"]
    if overall:
        print(f"📈 总记录数: {overall.get('total_records', 0)}")
        print(f"🕐 最早更新时间: {overall.get('earliest_update', 'N/A')}")
        print(f"🕐 最新更新时间: {overall.get('latest_update', 'N/A')}")
        print(f"📅 总更新天数: {overall.get('total_update_days', 0)}")
    
    daily = stats["daily_stats"]
    if daily:
        print(f"\n📅 近期每日更新记录数:")
        for day_stat in daily:
            print(f"  {day_stat.get('update_date', 'N/A')}: {day_stat.get('records_per_day', 0)} 条记录")
    
    print("=" * 50)