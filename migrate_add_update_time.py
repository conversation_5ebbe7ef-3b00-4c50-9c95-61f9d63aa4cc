#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移脚本：为现有表添加更新时间字段
"""

import sys
import os
sys.path.append('/mnt/Data2/data_auto_download')

from utils import mysql_execute, mysql_read_data

def add_update_time_column(table_name):
    """
    为指定表添加更新时间字段
    
    参数:
    - table_name: 表名
    """
    try:
        # 检查字段是否已存在
        check_sql = f"""
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = '{table_name}' 
        AND COLUMN_NAME = 'last_update_time'
        """
        
        result = mysql_read_data(check_sql)
        
        if not result.empty:
            print(f"✅ 表 {table_name} 已经存在 last_update_time 字段，跳过...")
            return True
        
        # 添加字段
        alter_sql = f"""
        ALTER TABLE {table_name} 
        ADD COLUMN last_update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间'
        """
        
        mysql_execute(alter_sql)
        print(f"✅ 成功为表 {table_name} 添加 last_update_time 字段")
        
        # 为现有数据设置更新时间
        update_sql = f"""
        UPDATE {table_name} 
        SET last_update_time = CURRENT_TIMESTAMP 
        WHERE last_update_time IS NULL
        """
        
        mysql_execute(update_sql)
        print(f"✅ 已为表 {table_name} 的现有数据设置更新时间")
        
        return True
        
    except Exception as e:
        print(f"❌ 为表 {table_name} 添加更新时间字段失败: {e}")
        return False

def migrate_all_tables():
    """为所有相关表添加更新时间字段"""
    
    tables = [
        'stock_company',
        'stock_basic', 
        'stock_hs_const',
        'trade_cal',
        'stock_namechange',
        'stock_managers'
    ]
    
    print("🔄 开始为所有表添加更新时间字段...")
    print("=" * 60)
    
    success_count = 0
    
    for table in tables:
        print(f"\n📊 处理表: {table}")
        if add_update_time_column(table):
            success_count += 1
    
    print(f"\n🎉 迁移完成! 成功处理 {success_count}/{len(tables)} 个表")

def check_migration_result():
    """检查迁移结果"""
    
    tables = [
        'stock_company',
        'stock_basic', 
        'stock_hs_const',
        'trade_cal',
        'stock_namechange',
        'stock_managers'
    ]
    
    print("\n🔍 检查迁移结果...")
    print("=" * 60)
    
    for table in tables:
        try:
            # 检查字段是否存在
            check_sql = f"""
            SELECT COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT, EXTRA
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = '{table}' 
            AND COLUMN_NAME = 'last_update_time'
            """
            
            result = mysql_read_data(check_sql)
            
            if not result.empty:
                row = result.iloc[0]
                print(f"✅ {table}: 字段已存在")
                print(f"   类型: {row['DATA_TYPE']}")
                print(f"   默认值: {row['COLUMN_DEFAULT']}")
                print(f"   额外属性: {row['EXTRA']}")
                
                # 检查是否有数据
                count_sql = f"SELECT COUNT(*) as count FROM {table} WHERE last_update_time IS NOT NULL"
                count_result = mysql_read_data(count_sql)
                if not count_result.empty:
                    count = count_result.iloc[0]['count']
                    print(f"   已设置更新时间的记录数: {count}")
            else:
                print(f"❌ {table}: 字段不存在")
                
        except Exception as e:
            print(f"❌ 检查表 {table} 时出错: {e}")
        
        print()

if __name__ == '__main__':
    print("🚀 开始数据库迁移：添加更新时间字段\n")
    
    # 执行迁移
    migrate_all_tables()
    
    # 检查结果
    check_migration_result()
    
    print("✨ 迁移完成!")
    print("\n📝 接下来请运行测试脚本验证功能:")
    print("python test_update_time.py") 